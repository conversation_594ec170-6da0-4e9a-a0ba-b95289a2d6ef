package com.conch.client.ui

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.WindowManager
import com.conch.client.databinding.BasicOverlayBinding

/**
 * 基础示例弹窗 - 完全照搬simple项目的实现
 */
class BasicExampleDialog(
    context: Context,
    private val onCommandExecute: (String) -> Unit
) : Dialog(context, android.R.style.Theme_Black_NoTitleBar_Fullscreen) {

    private lateinit var viewBinding: BasicOverlayBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        viewBinding = BasicOverlayBinding.inflate(LayoutInflater.from(context))
        setContentView(viewBinding.root)

        // 设置窗口属性，模拟simple项目的AssistsWindowWrapper
        window?.let { window ->
            val layoutParams = window.attributes
            layoutParams.width = (context.resources.displayMetrics.widthPixels * 0.8).toInt()
            layoutParams.height = (context.resources.displayMetrics.heightPixels * 0.5).toInt()
            layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
            window.attributes = layoutParams
        }

        setupClickListeners()
    }

    private fun setupClickListeners() {
        // 返回按钮
        viewBinding.btnBack.setOnClickListener {
            onCommandExecute("back")
            dismiss()
        }

        // 桌面按钮
        viewBinding.btnHome.setOnClickListener {
            onCommandExecute("home")
            dismiss()
        }

        // 显示通知按钮
        viewBinding.btnNotification.setOnClickListener {
            onCommandExecute("show_notifications")
            dismiss()
        }

        // 锁定屏幕按钮
        viewBinding.btnLockScreen.setOnClickListener {
            onCommandExecute("lock_screen")
            dismiss()
        }

        // 截取屏幕截图按钮
        viewBinding.btnTakeScreenshot.setOnClickListener {
            onCommandExecute("take_screenshot")
            dismiss()
        }
    }
}
