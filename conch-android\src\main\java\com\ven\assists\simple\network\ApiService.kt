package com.ven.assists.simple.network

import retrofit2.Response
import retrofit2.http.*

/**
 * REST API接口定义
 */
interface ApiService {
    
    /**
     * 发送指令信息到服务端
     */
    @POST("api/command")
    suspend fun sendCommand(@Body command: CommandRequest): Response<CommandResponse>
    
    /**
     * 获取服务端状态
     */
    @GET("api/status")
    suspend fun getServerStatus(): Response<ServerStatus>
    
    /**
     * 心跳检测
     */
    @POST("api/heartbeat")
    suspend fun heartbeat(@Body heartbeat: HeartbeatRequest): Response<HeartbeatResponse>
}

/**
 * 指令请求数据类
 */
data class CommandRequest(
    val commandId: String,
    val commandType: String,
    val commandData: String,
    val timestamp: Long,
    val deviceId: String
)

/**
 * 指令响应数据类
 */
data class CommandResponse(
    val success: Boolean,
    val message: String,
    val data: Any? = null
)

/**
 * 服务端状态数据类
 */
data class ServerStatus(
    val status: String,
    val version: String,
    val timestamp: Long
)

/**
 * 心跳请求数据类
 */
data class HeartbeatRequest(
    val deviceId: String,
    val timestamp: Long
)

/**
 * 心跳响应数据类
 */
data class HeartbeatResponse(
    val success: Boolean,
    val serverTime: Long
)
