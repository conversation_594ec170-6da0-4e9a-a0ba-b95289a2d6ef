package com.ven.assists.simple

import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.ven.assists.simple.network.NetworkManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 网络测试Activity
 * 用于测试与服务端的连接
 */
class NetworkTestActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "NetworkTestActivity"
    }
    
    private lateinit var networkManager: NetworkManager
    private lateinit var statusText: TextView
    private lateinit var testButton: Button
    private lateinit var sendCommandButton: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 创建简单的布局
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }
        
        statusText = TextView(this).apply {
            text = "网络状态: 未知"
            textSize = 16f
            setPadding(0, 20, 0, 20)
        }
        
        testButton = Button(this).apply {
            text = "测试服务端连接"
            setOnClickListener { testServerConnection() }
        }
        
        sendCommandButton = Button(this).apply {
            text = "发送测试指令"
            setOnClickListener { sendTestCommand() }
        }
        
        layout.addView(statusText)
        layout.addView(testButton)
        layout.addView(sendCommandButton)
        
        setContentView(layout)
        
        // 初始化网络管理器
        networkManager = NetworkManager.getInstance(this)
        
        // 检查初始连接状态
        updateConnectionStatus()
    }
    
    private fun updateConnectionStatus() {
        val isConnected = networkManager.isConnected()
        statusText.text = "网络状态: ${if (isConnected) "已连接" else "未连接"}"
    }
    
    private fun testServerConnection() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = networkManager.getServerStatus()
                
                withContext(Dispatchers.Main) {
                    result.onSuccess { status ->
                        statusText.text = "服务端状态: ${status.status}\n版本: ${status.server.version}\n名称: ${status.server.name}"
                        Log.d(TAG, "服务端连接成功: $status")
                    }.onFailure { error ->
                        statusText.text = "连接失败: ${error.message}"
                        Log.e(TAG, "服务端连接失败", error)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    statusText.text = "连接异常: ${e.message}"
                    Log.e(TAG, "连接异常", e)
                }
            }
        }
    }
    
    private fun sendTestCommand() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = networkManager.sendCommand(
                    "TEST_COMMAND",
                    """{"message": "这是一个测试指令", "timestamp": ${System.currentTimeMillis()}}"""
                )
                
                withContext(Dispatchers.Main) {
                    result.onSuccess { response ->
                        statusText.text = "指令发送成功: ${response.message}\n会话ID: ${response.sessionId}\n状态: ${response.state}"
                        Log.d(TAG, "测试指令发送成功: $response")
                    }.onFailure { error ->
                        statusText.text = "指令发送失败: ${error.message}"
                        Log.e(TAG, "测试指令发送失败", error)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    statusText.text = "发送异常: ${e.message}"
                    Log.e(TAG, "发送异常", e)
                }
            }
        }
    }
}
