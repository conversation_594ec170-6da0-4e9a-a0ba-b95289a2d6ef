package com.ven.assists.simple.network

import android.util.Log
import com.google.gson.Gson
import org.java_websocket.client.WebSocketClient
import org.java_websocket.handshake.ServerHandshake
import java.net.URI
import java.util.concurrent.TimeUnit

/**
 * WebSocket客户端
 */
class ConchWebSocketClient private constructor() {
    
    companion object {
        private const val TAG = "ConchWebSocketClient"
        private const val RECONNECT_INTERVAL = 5000L // 5秒重连间隔
        
        @Volatile
        private var INSTANCE: ConchWebSocketClient? = null
        
        fun getInstance(): ConchWebSocketClient {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ConchWebSocketClient().also { INSTANCE = it }
            }
        }
    }
    
    private var webSocketClient: WebSocketClient? = null
    private var serverUri: URI? = null
    private val gson = Gson()
    private var isReconnecting = false
    private var listeners = mutableListOf<WebSocketListener>()
    
    /**
     * WebSocket事件监听器
     */
    interface WebSocketListener {
        fun onConnected()
        fun onDisconnected()
        fun onMessageReceived(message: String)
        fun onError(error: Exception)
    }
    
    /**
     * 连接WebSocket服务器
     */
    fun connect(serverUrl: String) {
        try {
            serverUri = URI(serverUrl)
            createWebSocketClient()
            webSocketClient?.connect()
        } catch (e: Exception) {
            Log.e(TAG, "连接WebSocket失败", e)
            notifyError(e)
        }
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        isReconnecting = false
        webSocketClient?.close()
        webSocketClient = null
    }
    
    /**
     * 发送消息
     */
    fun sendMessage(message: String): Boolean {
        return try {
            webSocketClient?.send(message)
            true
        } catch (e: Exception) {
            Log.e(TAG, "发送消息失败", e)
            false
        }
    }
    
    /**
     * 发送指令信息
     */
    fun sendCommand(command: CommandRequest): Boolean {
        return try {
            val json = gson.toJson(command)
            sendMessage(json)
        } catch (e: Exception) {
            Log.e(TAG, "发送指令失败", e)
            false
        }
    }
    
    /**
     * 添加监听器
     */
    fun addListener(listener: WebSocketListener) {
        listeners.add(listener)
    }
    
    /**
     * 移除监听器
     */
    fun removeListener(listener: WebSocketListener) {
        listeners.remove(listener)
    }
    
    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean {
        return webSocketClient?.isOpen == true
    }
    
    private fun createWebSocketClient() {
        webSocketClient = object : WebSocketClient(serverUri) {
            override fun onOpen(handshake: ServerHandshake?) {
                Log.d(TAG, "WebSocket连接已建立")
                isReconnecting = false
                notifyConnected()
            }
            
            override fun onMessage(message: String?) {
                Log.d(TAG, "收到消息: $message")
                message?.let { notifyMessageReceived(it) }
            }
            
            override fun onClose(code: Int, reason: String?, remote: Boolean) {
                Log.d(TAG, "WebSocket连接已关闭: $reason")
                notifyDisconnected()
                
                // 自动重连
                if (!isReconnecting) {
                    scheduleReconnect()
                }
            }
            
            override fun onError(ex: Exception?) {
                Log.e(TAG, "WebSocket错误", ex)
                ex?.let { notifyError(it) }
            }
        }
    }
    
    private fun scheduleReconnect() {
        if (isReconnecting) return
        
        isReconnecting = true
        Thread {
            try {
                Thread.sleep(RECONNECT_INTERVAL)
                if (isReconnecting && serverUri != null) {
                    Log.d(TAG, "尝试重新连接WebSocket")
                    createWebSocketClient()
                    webSocketClient?.connect()
                }
            } catch (e: InterruptedException) {
                Log.d(TAG, "重连被中断")
            }
        }.start()
    }
    
    private fun notifyConnected() {
        listeners.forEach { it.onConnected() }
    }
    
    private fun notifyDisconnected() {
        listeners.forEach { it.onDisconnected() }
    }
    
    private fun notifyMessageReceived(message: String) {
        listeners.forEach { it.onMessageReceived(message) }
    }
    
    private fun notifyError(error: Exception) {
        listeners.forEach { it.onError(error) }
    }
}
