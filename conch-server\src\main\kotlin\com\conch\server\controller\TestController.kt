package com.conch.server.controller

import com.conch.server.config.ConchProperties
import com.conch.server.dto.*
import com.conch.server.util.LogUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime

@RestController
@RequestMapping("/api/v1")
@CrossOrigin(origins = ["*"])
class TestController @Autowired constructor(
    private val conchProperties: ConchProperties
) {

    companion object {
        private val logger = LoggerFactory.getLogger(TestController::class.java)
    }

    @GetMapping("/health")
    fun healthCheck(): HealthCheckResponseDto {
        logger.info("健康检查")
        return HealthCheckResponseDto(
            status = "OK",
            timestamp = LocalDateTime.now(),
            server = ServerInfoDto(
                name = conchProperties.server.name,
                version = conchProperties.server.version,
                description = conchProperties.server.description
            ),
            config = ConfigInfoDto(
                sessionTimeout = conchProperties.session.timeout,
                maxConcurrentSessions = conchProperties.session.maxConcurrent,
                maxScriptActions = conchProperties.script.maxActions,
                defaultActionTimeout = conchProperties.script.defaultTimeout
            )
        )
    }

    @PostMapping("/voice/command")
    fun submitTextCommand(@RequestBody request: CommandRequestDto): CommandResponseDto {
        val sessionId = "session_${System.currentTimeMillis()}"

        // 使用专门的日志工具，确保中文正确显示
        LogUtil.logCommand(
            action = "指令接收",
            deviceModel = request.deviceInfo.model,
            command = request.textCommand.text,
            sessionId = sessionId,
            timestamp = LocalDateTime.now().toString()
        )

        return CommandResponseDto(
            sessionId = sessionId,
            state = "PROCESSING",
            message = "指令已接收，正在处理...",
            estimatedDuration = 10000
        )
    }

    @GetMapping("/script/{sessionId}")
    fun getExecutionScript(@PathVariable sessionId: String): ExecutionScriptDto {
        val scriptId = "script_${System.currentTimeMillis()}"

        // 返回一个简单的测试脚本
        val script = ExecutionScriptDto(
            sessionId = sessionId,
            scriptId = scriptId,
            actions = listOf(
                ActionDto(
                    type = "WAIT",
                    target = ActionTargetDto(
                        type = "COORDINATE",
                        value = "",
                        x = 0,
                        y = 0
                    ),
                    parameters = mapOf("duration" to 2000),
                    timeout = 5000
                ),
                ActionDto(
                    type = "CLICK",
                    target = ActionTargetDto(
                        type = "TEXT",
                        value = "测试按钮",
                        x = 0,
                        y = 0
                    ),
                    parameters = emptyMap(),
                    timeout = 5000
                )
            ),
            metadata = ScriptMetadataDto(
                estimatedDuration = 7000,
                retryCount = 3,
                priority = "NORMAL"
            )
        )

        LogUtil.logScriptGeneration(sessionId, scriptId, script.actions.size)
        return script
    }

    @PostMapping("/feedback")
    fun submitFeedback(@RequestBody feedback: FeedbackRequestDto): ApiResponseDto<String> {
        LogUtil.logFeedback(
            sessionId = feedback.sessionId,
            scriptId = feedback.scriptId,
            status = feedback.executionResult.status,
            executionTime = feedback.executionResult.executionTime,
            completedActions = feedback.executionResult.completedActions,
            totalActions = feedback.executionResult.totalActions
        )

        return ApiResponseDto(
            success = true,
            data = "反馈已接收",
            message = "执行反馈处理成功"
        )
    }

    @PostMapping("/session/complete")
    fun completeSession(@RequestBody completion: Map<String, Any>): Map<String, String> {
        val sessionId = completion["sessionId"] as? String ?: "未知会话"
        val success = completion["success"] as? Boolean ?: false
        val finalMessage = completion["finalMessage"] as? String ?: ""

        println("🎯 [会话完成] 会话执行完成")
        println("🆔 会话ID: $sessionId")
        println("✅ 执行结果: ${if (success) "成功" else "失败"}")
        println("💬 最终消息: $finalMessage")
        println("⏰ 完成时间: ${LocalDateTime.now()}")
        println("🎉 会话已结束")
        println("=".repeat(60))

        return mapOf("status" to "OK")
    }

    @PostMapping("/session/{id}/cancel")
    fun cancelSession(@PathVariable id: String): Map<String, String> {
        println("❌ [会话取消] 用户取消了会话: $id")
        println("⏰ 取消时间: ${LocalDateTime.now()}")
        return mapOf("status" to "OK")
    }
}
