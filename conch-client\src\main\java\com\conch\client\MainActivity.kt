package com.conch.client

import android.os.Bundle
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.conch.client.ui.theme.ConchTheme
import com.conch.client.ui.MainUiState
import com.conch.client.ui.BasicExampleDialog
import com.conch.client.config.NetworkConfig
import com.conch.client.network.ApiClient
import kotlinx.coroutines.launch
import android.util.Log

class MainActivity : ComponentActivity() {

    private var isFullscreenSetup = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化网络配置
        NetworkConfig.init(this)

        setContent {
            ConchTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background,
                ) {
                    SimpleMainScreen()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // 只在首次或需要时设置全屏模式，避免重复设置
        if (!isFullscreenSetup) {
            setupFullscreen()
            isFullscreenSetup = true
        }
    }

    override fun onPause() {
        super.onPause()
        // 可以在这里处理暂停时的逻辑，比如保存状态
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理资源
        isFullscreenSetup = false
    }

    /**
     * 设置沉浸式全屏模式
     */
    private fun setupFullscreen() {
        // 启用边到边显示
        WindowCompat.setDecorFitsSystemWindows(window, false)

        // 获取窗口控制器
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)

        // 配置系统栏行为
        // 隐藏状态栏和导航栏
        windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())

        // 设置系统栏行为：滑动时显示，自动隐藏
        windowInsetsController.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE

        // 保持屏幕常亮（可选）
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        // 设置状态栏和导航栏为透明
        window.statusBarColor = android.graphics.Color.TRANSPARENT
        window.navigationBarColor = android.graphics.Color.TRANSPARENT
    }
}

// 发送指令到服务端
suspend fun sendCommandToServer(command: String): String {
    val deviceInfo = com.conch.client.model.DeviceInfo(
        model = android.os.Build.MODEL,
        androidVersion = "Android ${android.os.Build.VERSION.RELEASE}",
        screenResolution = "1080x2400", // 可以动态获取
        installedApps = emptyList(),
    )

    val request = com.conch.client.model.CommandRequest(
        text = command,
        deviceInfo = deviceInfo,
    )

    Log.d("NetworkRequest", "发送指令: $command")
    Log.d("NetworkRequest", "设备信息: ${deviceInfo.model}")

    return when (val result = ApiClient.sendCommand(request)) {
        is com.conch.client.network.NetworkResult.Success -> {
            Log.d("NetworkRequest", "请求成功: ${result.data}")
            "服务端响应: ${result.data.message} (会话ID: ${result.data.sessionId})"
        }
        is com.conch.client.network.NetworkResult.Error -> {
            Log.e("NetworkRequest", "网络请求失败", result.exception)
            "网络连接失败: ${result.exception.message}"
        }
    }
}

@Composable
fun SimpleMainScreen() {
    var uiState by remember { mutableStateOf(MainUiState()) }
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current

    // 检查网络连接状态
    LaunchedEffect(Unit) {
        when (val result = ApiClient.checkConnection()) {
            is com.conch.client.network.NetworkResult.Success -> {
                uiState = uiState.updateNetworkStatus(if (result.data) "已连接" else "连接失败")
            }
            is com.conch.client.network.NetworkResult.Error -> {
                uiState = uiState.updateNetworkStatus("连接失败: ${result.exception.message}")
            }
        }
    }

    // 真实的服务端通信过程
    fun executeCommand(command: String) {
        coroutineScope.launch {
            try {
                uiState = uiState.startProcessing()
                uiState = uiState.addLog("🚀 开始处理指令: $command")
                uiState = uiState.updateProgress(0.1f)

                // 1. 发送指令到服务端
                uiState = uiState.addLog("📡 正在连接服务端...")
                uiState = uiState.updateProgress(0.3f)

                val response = sendCommandToServer(command)
                uiState = uiState.addLog("✅ 服务端响应: $response")
                uiState = uiState.updateProgress(0.6f)

                kotlinx.coroutines.delay(1000)

                // 2. 模拟执行过程
                uiState = uiState.addLog("⚡ 开始执行自动化操作...")
                uiState = uiState.updateProgress(0.8f)
                kotlinx.coroutines.delay(2000)

                uiState = uiState.addLog("🎉 执行完成！")
                uiState = uiState.updateProgress(1.0f)
                kotlinx.coroutines.delay(1000)

                // 重置状态
                uiState = uiState.completeProcessing()
            } catch (e: Exception) {
                Log.e("ExecuteCommand", "执行失败", e)
                uiState = uiState.addLog("❌ 执行失败: ${e.message}")
                uiState = uiState.completeProcessing()
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
    ) {
        // 标题
        Text(
            text = "小田螺助手",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.align(Alignment.CenterHorizontally),
        )

        // 网络状态显示
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = if (uiState.networkStatus.contains("已连接")) {
                    MaterialTheme.colorScheme.primaryContainer
                } else {
                    MaterialTheme.colorScheme.errorContainer
                },
            ),
        ) {
            Column(
                modifier = Modifier.padding(12.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = "服务端状态: ${uiState.networkStatus}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (uiState.networkStatus.contains("已连接")) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onErrorContainer
                    },
                )
                Text(
                    text = "服务地址: ${NetworkConfig.getBaseUrl()}",
                    style = MaterialTheme.typography.bodySmall,
                    color = if (uiState.networkStatus.contains("已连接")) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onErrorContainer
                    },
                )

                // 添加重新连接按钮
                if (!uiState.networkStatus.contains("已连接")) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                        onClick = {
                            coroutineScope.launch {
                                uiState = uiState.updateNetworkStatus("检查中...")
                                when (val result = ApiClient.checkConnection()) {
                                    is com.conch.client.network.NetworkResult.Success -> {
                                        uiState = uiState.updateNetworkStatus(if (result.data) "已连接" else "连接失败")
                                    }
                                    is com.conch.client.network.NetworkResult.Error -> {
                                        uiState = uiState.updateNetworkStatus("连接失败: ${result.exception.message}")
                                    }
                                }
                            }
                        },
                        modifier = Modifier.size(width = 120.dp, height = 32.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary,
                        ),
                    ) {
                        Text(
                            text = "重新连接",
                            style = MaterialTheme.typography.bodySmall,
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 输入区域
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                OutlinedTextField(
                    value = uiState.inputText,
                    onValueChange = { uiState = uiState.updateInputText(it) },
                    label = { Text("请输入指令") },
                    placeholder = { Text("例如：open gallery, send message, open taobao") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 2,
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 添加快捷指令按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    Button(
                        onClick = { uiState = uiState.updateInputText("open gallery") },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary,
                        ),
                    ) {
                        Text("Gallery", style = MaterialTheme.typography.bodySmall)
                    }

                    Button(
                        onClick = { uiState = uiState.updateInputText("send message") },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary,
                        ),
                    ) {
                        Text("Message", style = MaterialTheme.typography.bodySmall)
                    }

                    Button(
                        onClick = { uiState = uiState.updateInputText("open camera") },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary,
                        ),
                    ) {
                        Text("Camera", style = MaterialTheme.typography.bodySmall)
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 基础示例按钮 - 完全照搬simple项目的实现
                Button(
                    onClick = {
                        val dialog = BasicExampleDialog(context) { command ->
                            executeCommand(command)
                        }
                        dialog.show()
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.tertiary,
                    ),
                ) {
                    Text("基础示例", style = MaterialTheme.typography.bodyMedium)
                }



                Spacer(modifier = Modifier.height(16.dp))

                Button(
                    onClick = {
                        if (!uiState.isProcessing && !uiState.isExecuting) {
                            executeCommand(uiState.inputText)
                        }
                    },
                    enabled = uiState.inputText.isNotBlank() && !uiState.isProcessing && !uiState.isExecuting,
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    if (uiState.isProcessing) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = MaterialTheme.colorScheme.onPrimary,
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("执行中...")
                    } else {
                        Text("执行指令")
                    }
                }

                Text(
                    text = "演示版本 - 模拟执行效果",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                )
            }
        }

        // 执行进度
        if (uiState.isExecuting) {
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "执行进度",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    LinearProgressIndicator(
                        progress = { uiState.executionProgress },
                        modifier = Modifier.fillMaxWidth(),
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "${(uiState.executionProgress * 100).toInt()}%",
                        style = MaterialTheme.typography.bodyMedium,
                    )
                }
            }
        }

        // 执行日志
        if (uiState.executionLogs.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = "执行日志 (${uiState.executionLogs.size}/50)",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                        )

                        // 添加清空日志按钮
                        if (uiState.executionLogs.isNotEmpty()) {
                            Button(
                                onClick = { uiState = uiState.clearLogs() },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = MaterialTheme.colorScheme.secondary,
                                ),
                                modifier = Modifier.size(width = 80.dp, height = 32.dp),
                            ) {
                                Text("清空", style = MaterialTheme.typography.bodySmall)
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    LazyColumn(
                        modifier = Modifier.height(120.dp),
                    ) {
                        items(uiState.executionLogs) { log ->
                            Text(
                                text = "• $log",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.padding(vertical = 2.dp),
                            )
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // 底部说明
        Text(
            text = "小田螺助手 v1.0 - 智能手机自动化助手\n当前为演示模式，展示完整的执行流程",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.align(Alignment.CenterHorizontally),
        )
    }


}


