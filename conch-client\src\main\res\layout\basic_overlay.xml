<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.flexbox.FlexboxLayout
            android:id="@+id/fl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:flexWrap="wrap">

            <Button
                android:id="@+id/btn_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="返回" />

            <Button
                android:id="@+id/btn_home"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="桌面" />

            <Button
                android:id="@+id/btn_notification"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="显示通知" />

            <Button
                android:id="@+id/btn_lock_screen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="锁定屏幕" />

            <Button
                android:id="@+id/btn_take_screenshot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="截取屏幕截图" />

        </com.google.android.flexbox.FlexboxLayout>

    </androidx.core.widget.NestedScrollView>
</FrameLayout>
